# 枫叶快速开发平台 - 登录页面

## 功能特性

### 🎨 视觉设计
- **现代化渐变背景**: 采用蓝色渐变背景，配合动态浮动效果
- **毛玻璃效果**: 登录表单采用半透明毛玻璃设计，现代感十足
- **响应式布局**: 支持桌面端和移动端自适应
- **动画效果**: 包含背景浮动动画和按钮悬停效果

### 🔐 登录功能
- **用户名密码登录**: 支持传统的用户名密码登录方式
- **表单验证**: 
  - 用户名：3-20个字符，必填
  - 密码：6-20个字符，必填，支持显示/隐藏
- **记住密码**: 可选择记住登录状态
- **第三方登录**: 预留QQ、Google、微信登录入口

### 🌐 国际化支持
- **语言切换**: 支持中文/英文切换（预留功能）
- **本地化界面**: 所有文本支持多语言

## 技术栈

- **Vue 3**: 使用 Composition API
- **Element Plus**: UI组件库
- **SCSS**: 样式预处理器
- **Vite**: 构建工具

## 文件结构

```
src/views/LoginView.vue
├── template          # 模板结构
│   ├── 背景容器
│   ├── 登录表单容器
│   ├── 表单字段
│   ├── 第三方登录
│   └── 语言切换
├── script            # 逻辑代码
│   ├── 响应式数据
│   ├── 表单验证规则
│   └── 登录处理函数
└── style            # 样式定义
    ├── 容器样式
    ├── 表单样式
    ├── 动画效果
    └── 响应式设计
```

## 使用方法

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 访问登录页面
打开浏览器访问: `http://localhost:5173/`

### 3. 测试登录功能
- 输入用户名（3-20个字符）
- 输入密码（6-20个字符）
- 点击登录按钮
- 查看登录成功提示

## 自定义配置

### 修改主题色彩
在 `LoginView.vue` 的 style 部分修改以下变量：

```scss
// 背景渐变色
background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

// 登录按钮渐变色
background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
```

### 修改表单验证规则
在 script 部分的 `loginRules` 对象中修改：

```javascript
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}
```

### 集成后端API
在 `handleLogin` 函数中替换模拟登录逻辑：

```javascript
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (valid) {
      loading.value = true
      
      // 替换为实际的API调用
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: loginForm.username,
          password: loginForm.password,
          rememberMe: loginForm.rememberMe
        })
      })
      
      const result = await response.json()
      
      if (result.success) {
        ElMessage.success('登录成功！')
        // 跳转到主页面
        router.push('/dashboard')
      } else {
        ElMessage.error(result.message || '登录失败')
      }
      
      loading.value = false
    }
  } catch (error) {
    loading.value = false
    ElMessage.error('网络错误，请稍后重试')
    console.error('登录错误:', error)
  }
}
```

## 浏览器兼容性

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 注意事项

1. 确保已安装 `sass` 依赖包
2. Element Plus 需要正确配置
3. 图标组件需要从 `@element-plus/icons-vue` 导入
4. 生产环境需要配置实际的登录API接口

## 更新日志

### v1.0.0 (2025-01-07)
- ✨ 初始版本发布
- 🎨 现代化UI设计
- 🔐 基础登录功能
- 📱 响应式布局
- 🌐 国际化预留
