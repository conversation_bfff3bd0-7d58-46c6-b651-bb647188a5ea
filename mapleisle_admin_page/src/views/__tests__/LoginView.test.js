import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElMessage } from 'element-plus'
import LoginView from '../LoginView.vue'

// Mock Element Plus Message
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn()
  }
}))

describe('LoginView', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mount(LoginView, {
      global: {
        stubs: {
          'el-form': true,
          'el-form-item': true,
          'el-input': true,
          'el-button': true,
          'el-checkbox': true,
          'el-dropdown': true,
          'el-dropdown-menu': true,
          'el-dropdown-item': true,
          'el-icon': true
        }
      }
    })
  })

  it('renders login form correctly', () => {
    expect(wrapper.find('.login-container').exists()).toBe(true)
    expect(wrapper.find('.login-form-container').exists()).toBe(true)
    expect(wrapper.find('.login-title h2').text()).toBe('枫叶快速开发平台')
  })

  it('has correct initial form data', () => {
    const vm = wrapper.vm
    expect(vm.loginForm.username).toBe('')
    expect(vm.loginForm.password).toBe('')
    expect(vm.loginForm.rememberMe).toBe(false)
  })

  it('validates form rules correctly', () => {
    const vm = wrapper.vm
    const rules = vm.loginRules
    
    // Test username rules
    expect(rules.username).toHaveLength(2)
    expect(rules.username[0].required).toBe(true)
    expect(rules.username[1].min).toBe(3)
    expect(rules.username[1].max).toBe(20)
    
    // Test password rules
    expect(rules.password).toHaveLength(2)
    expect(rules.password[0].required).toBe(true)
    expect(rules.password[1].min).toBe(6)
    expect(rules.password[1].max).toBe(20)
  })
})
