<template>
  <div class="login-container">
    <!-- 背景图片 -->
    <div class="background-image"></div>

    <!-- 登录表单容器 -->
    <div class="login-form-container">
      <div class="login-form">
        <!-- 标题 -->
        <div class="login-title">
          <h2>枫叶快速开发平台</h2>
        </div>

        <!-- 登录表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form-content"
          size="large"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              clearable
            >
              <template #prefix>
                <el-icon><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              show-password
              clearable
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-checkbox v-model="loginForm.rememberMe">
              记住密码
            </el-checkbox>
          </el-form-item>

          <!-- 第三方登录 -->
          <div class="third-party-login">
            <span class="third-party-text">第三方登录方式</span>
            <div class="third-party-icons">
              <el-button circle class="qq-btn">
                <span class="icon-qq">QQ</span>
              </el-button>
              <el-button circle class="google-btn">
                <span class="icon-google">G</span>
              </el-button>
              <el-button circle class="wechat-btn">
                <span class="icon-wechat">微</span>
              </el-button>
            </div>
          </div>

          <el-form-item>
            <el-button
              type="primary"
              class="login-btn"
              :loading="loading"
              @click="handleLogin"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 语言切换 -->
        <div class="language-switch">
          <el-dropdown>
            <span class="language-text">
              中文 <el-icon><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>中文</el-dropdown-item>
                <el-dropdown-item>English</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Lock, ArrowDown } from '@element-plus/icons-vue'
import {login} from '@/api/auth/index.js'

// 表单引用
const loginFormRef = ref()

// 加载状态
const loading = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (valid) {
      loading.value = true
      ElMessage.info('开始登录')

      try {
        const res = await login(loginForm)
        loading.value = false

        if(res.data.code === 200){
          ElMessage.success('登录成功！')
          // 将 token 存储到 pinia
          // 跳转到主页面
          // router.push('/dashboard')
        } else {
          ElMessage.error(res.data.msg || res.data.message || '登录失败')
        }
      } catch (error) {
        loading.value = false
        ElMessage.error('网络错误，请稍后重试')
        console.error('登录错误:', error)
      }
    }
  } catch (error) {
    ElMessage.error('表单验证失败')
    console.log('表单验证失败:', error)
  }
}
</script>

<style lang="scss">
// 全局样式重置，确保页面完全适配
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

#app {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
</style>

<style lang="scss" scoped>
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 8%;
  margin: 0;
  box-sizing: border-box;
}

.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-image: url('https://raw.githubusercontent.com/baifeng66/imgs/master/imgs/202506271712651.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
  }
}



.login-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.3);
  padding: 40px 35px;
  width: 400px;
  min-height: 520px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
  z-index: 10;
}

.login-form {
  width: 100%;
}

.login-title {
  text-align: center;
  margin-bottom: 40px;

  h2 {
    color: #333;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    letter-spacing: 1px;
  }
}

.login-form-content {
  .el-form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-input {
    height: 48px;

    :deep(.el-input__wrapper) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e6ed;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &.is-focus {
        box-shadow: 0 4px 12px rgba(103, 126, 234, 0.3);
        border-color: #667eea;
      }
    }

    :deep(.el-input__inner) {
      height: 46px;
      line-height: 46px;
      font-size: 14px;
    }
  }

  .el-checkbox {
    :deep(.el-checkbox__label) {
      color: #666;
      font-size: 14px;
    }
  }
}

.third-party-login {
  text-align: center;
  margin: 30px 0;

  .third-party-text {
    color: #999;
    font-size: 12px;
    display: block;
    margin-bottom: 16px;
  }

  .third-party-icons {
    display: flex;
    justify-content: center;
    gap: 12px;

    .el-button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: none;
      font-size: 12px;
      font-weight: bold;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }

      &.qq-btn {
        background: #12b7f5;
        color: white;

        .icon-qq {
          font-size: 14px;
        }
      }

      &.google-btn {
        background: #db4437;
        color: white;

        .icon-google {
          font-size: 16px;
          font-weight: bold;
        }
      }

      &.wechat-btn {
        background: #1aad19;
        color: white;

        .icon-wechat {
          font-size: 12px;
        }
      }
    }
  }
}

.login-btn {
  width: 100%;
  height: 52px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 24px rgba(79, 172, 254, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(-1px);
  }
}

.language-switch {
  position: absolute;
  bottom: 10px;
  right: 20px;


  .language-text {
    color: #666;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;

    &:hover {
      color: #667eea;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    padding: 20px;
    justify-content: center;
    align-items: center;
  }

  .login-form-container {
    width: calc(100vw - 40px);
    max-width: 360px;
    min-height: auto;
    padding: 30px 25px;
    margin: 0;
  }

  .login-title h2 {
    font-size: 20px;
  }
}

@media (max-height: 600px) {
  .login-form-container {
    min-height: auto;
    padding: 25px 30px;
  }

  .login-title {
    margin-bottom: 25px;
  }

  .login-form-content .el-form-item {
    margin-bottom: 18px;
  }
}
</style>