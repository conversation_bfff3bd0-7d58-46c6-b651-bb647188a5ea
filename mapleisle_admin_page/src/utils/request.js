import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'
let token = 'sssd'

// 创建 axios
const request = axios.create({
  baseURL: 'http://localhost:8066/',
  // 是否跨域携带cookie
  withCredentials: false,
  timeout: 10000,
})

// 设置请求头参数类型+字符编码格式
axios.defaults.headers['Content-Type'] = 'application/json?charset=utf-8'

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    // token 应该从 pinia 里面取的
    if (token) {
      config.headers['Mapleisle-Authorization'] = token
    }
    return config
  },
  (error) => {
    // 对请求错误做些什么
    ElMessage.error(error.message)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 对响应数据做些什么
    let {code, msg} = response.data
    if (code === null || code === 200){
        return response
    }else if(code === 500){
        ElMessage.error(msg)
    }else if(code === 401){
        ElMessage.error('未授权')
    }else if(code === 403){
        ElMessage.error('登录过期')
        // 清除pinia中的数据
        window.sessionStorage.clear()
        // 跳转到登录页
        router.push('/login')
    }
    return Promise.reject(msg)
  },
  (error) => {
    // 对响应错误做些什么
    ElMessage.error(error.message)
    window.sessionStorage.clear()
        // 跳转到登录页
        router.push('/login')
    return Promise.reject(error)
  }
)

export default request
