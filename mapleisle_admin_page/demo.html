<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>枫叶快速开发平台 - 登录页面演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .demo-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .demo-content {
            padding: 30px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        .feature-card h3 {
            color: #333;
            margin-top: 0;
        }
        .demo-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #6c757d;
        }
        .screenshot {
            text-align: center;
            margin: 30px 0;
        }
        .screenshot img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .code-block pre {
            margin: 0;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🍁 枫叶快速开发平台</h1>
            <p>现代化 Vue3 + Element Plus 登录页面</p>
        </div>
        
        <div class="demo-content">
            <h2>✨ 功能特性</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🎨 现代化设计</h3>
                    <ul>
                        <li>渐变背景与动态效果</li>
                        <li>毛玻璃质感登录框</li>
                        <li>流畅的动画过渡</li>
                        <li>响应式布局设计</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🔐 完整登录功能</h3>
                    <ul>
                        <li>用户名密码验证</li>
                        <li>表单实时校验</li>
                        <li>记住密码选项</li>
                        <li>第三方登录预留</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🛠️ 技术栈</h3>
                    <ul>
                        <li>Vue 3 Composition API</li>
                        <li>Element Plus UI 组件</li>
                        <li>SCSS 样式预处理</li>
                        <li>Vite 构建工具</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🌐 用户体验</h3>
                    <ul>
                        <li>多语言支持预留</li>
                        <li>无障碍访问支持</li>
                        <li>移动端适配</li>
                        <li>现代浏览器兼容</li>
                    </ul>
                </div>
            </div>
            
            <div class="demo-buttons">
                <a href="http://localhost:5173/" class="btn" target="_blank">🚀 查看登录页面</a>
                <a href="LOGIN_PAGE_README.md" class="btn btn-secondary">📖 查看文档</a>
            </div>
            
            <h2>🔧 快速开始</h2>
            
            <div class="code-block">
                <pre># 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问页面
http://localhost:5173/</pre>
            </div>
            
            <h2>📝 测试登录</h2>
            <p>您可以使用以下测试数据来体验登录功能：</p>
            
            <div class="code-block">
                <pre>用户名: admin (3-20个字符)
密码: 123456 (6-20个字符)

点击登录按钮后会显示成功提示消息</pre>
            </div>
            
            <h2>🎯 主要组件</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>登录表单</h3>
                    <p>包含用户名、密码输入框，支持实时验证和错误提示</p>
                </div>
                
                <div class="feature-card">
                    <h3>第三方登录</h3>
                    <p>预留QQ、Google、微信登录按钮，可根据需要集成</p>
                </div>
                
                <div class="feature-card">
                    <h3>语言切换</h3>
                    <p>支持中英文切换，可扩展更多语言</p>
                </div>
                
                <div class="feature-card">
                    <h3>响应式设计</h3>
                    <p>自适应桌面端和移动端，提供最佳用户体验</p>
                </div>
            </div>
            
            <h2>🔄 自定义配置</h2>
            <p>登录页面支持多种自定义配置，包括：</p>
            <ul>
                <li><strong>主题色彩</strong>: 修改渐变背景和按钮颜色</li>
                <li><strong>验证规则</strong>: 自定义用户名和密码验证规则</li>
                <li><strong>API集成</strong>: 替换模拟登录为真实API调用</li>
                <li><strong>第三方登录</strong>: 集成OAuth登录服务</li>
            </ul>
            
            <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
                <p style="color: #666;">
                    © 2025 枫叶快速开发平台 | 基于 Vue 3 + Element Plus 构建
                </p>
            </div>
        </div>
    </div>
</body>
</html>
