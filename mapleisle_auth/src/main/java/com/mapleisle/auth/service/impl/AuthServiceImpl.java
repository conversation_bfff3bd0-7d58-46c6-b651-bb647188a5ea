package com.mapleisle.auth.service.impl;

import com.mapleisle.auth.service.IAuthService;
import com.mapleisle.common.domain.dto.LoginDto;
import com.mapleisle.common.response.MapleisleResult;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

/***
 *@title AuthServiceImpl
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/7 16:29
 **/
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements IAuthService {

    private final AuthenticationManager authenticationManager;

    @Override
    public MapleisleResult login(LoginDto loginDto) {
        // 1.
        UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(loginDto.getUsername(), loginDto.getPassword());
        // 2.
        Authentication authenticate = authenticationManager.authenticate(authentication);
        // 3.
        Object loginUser = authenticate.getPrincipal();
        return null;
    }
}
