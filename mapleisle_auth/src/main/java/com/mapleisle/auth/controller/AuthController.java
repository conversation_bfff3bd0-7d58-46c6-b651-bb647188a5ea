package com.mapleisle.auth.controller;

import com.mapleisle.auth.service.IAuthService;
import com.mapleisle.common.domain.dto.LoginDto;
import com.mapleisle.common.response.MapleisleResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/***
 *@title AuthController
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/7 15:34
 **/
@RestController
@Slf4j
@RequestMapping("/auth")
public class AuthController {

    @Resource
    private IAuthService authService;

    @PostMapping("/sys")
    public MapleisleResult login(@RequestBody LoginDto loginDto) {
        log.info("loginDto: {}", loginDto);
        return authService.login(loginDto);
    }
}
