package com.mapleisle.auth.controller;

import com.mapleisle.common.domain.dto.AddSysUserDto;
import com.mapleisle.common.response.MapleisleResult;
import com.mapleisle.common.service.IUmsSysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/***
 *@title UmsSysUserController
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/6 14:38
 **/
@RestController
@RequestMapping("/ums/sysUser")
@RequiredArgsConstructor
public class UmsSysUserController {
    private final IUmsSysUserService umsSysUserService;

    /**
     * @param sysUserDto
     * @return MapleisleResult
     * @description TODO
     * <AUTHOR>
     * @date 2025/8/6 15:01:15
     */
    @PostMapping
    public MapleisleResult addSysUser(@RequestBody AddSysUserDto sysUserDto) {
        return umsSysUserService.addSysUser(sysUserDto);
    }

    @GetMapping("/list")
    public MapleisleResult searchSysUserList() {
        return umsSysUserService.searchSysUserList();
    }
}
