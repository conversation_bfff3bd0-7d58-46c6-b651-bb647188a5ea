package com.mapleilse.common.exception;

import lombok.Getter;

import java.io.Serial;

/***
 *@title ServerException
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/5 15:42
 **/
@Getter
public class ServerException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 2950481628231130740L;

    private Integer code;
    private String message;
    private String detailMessage;

    public ServerException() {
    }

    public ServerException(String message) {
        this.message = message;
    }

    public ServerException(Integer code, String message) {
        this.code = code;
        this.message = message;
    }


    public ServerException setMessage(String message) {
        this.message = message;
        return this;
    }
}
