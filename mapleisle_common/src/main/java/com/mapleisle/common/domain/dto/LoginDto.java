package com.mapleisle.common.domain.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/***
 *@title LoginDto
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/7 15:31
 **/
@Data
public class LoginDto implements Serializable {
    @Serial
    private static final long serialVersionUID = -2301951223954300539L;

    private String username;
    private String password;
    private Boolean rememberMe;
}
