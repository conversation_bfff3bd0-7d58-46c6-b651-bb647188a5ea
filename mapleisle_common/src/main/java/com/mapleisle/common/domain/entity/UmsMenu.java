package com.mapleisle.common.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/***
 *@title UmsMenu
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/5 14:12
 **/
@Data
@TableName("ums_menu")
public class UmsMenu implements Serializable {
    @Serial
    private static final long serialVersionUID = -6013194146493642856L;
    @TableId
    private Long id;
    private Long parentId;
    @NotNull(message = "菜单名称不能为空")
    private String menuName;
    private Integer sort;
    @NotNull(message = "请选择菜单类型")
    private String menuType;
    private String path;
    private String componentPath;
    @NotNull(message = "权限不能为空")
    private String perms;
    @NotNull(message = "icon不能为空")
    private String icon;
    private Integer status;
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updater;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String remark;
    @TableLogic
    private Integer deleted;
}
