package com.mapleisle.common.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/***
 *@title UmsRole
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/5 14:08
 **/
@Data
@TableName("ums_role")
public class UmsRole implements Serializable {
    @Serial
    private static final long serialVersionUID = -3077885128389297399L;
    @TableId
    private Long roleId;
    private String roleName;
    private String roleLabel;
    private Integer sort;
    private Integer status;
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updater;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String remark;
    // 逻辑删除
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;
}
