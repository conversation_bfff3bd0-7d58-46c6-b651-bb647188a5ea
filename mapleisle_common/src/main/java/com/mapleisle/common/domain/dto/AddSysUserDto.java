package com.mapleisle.common.domain.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/***
 *@title AddSysUserDto
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/6 14:44
 **/
@Data
public class AddSysUserDto {
    @NotBlank(message = "用户账号不能为空")
    private String userName;
    @NotBlank(message = "用户昵称不能为空")
    private String nickName;
    @Email(message = "邮箱格式不正确")
    private String email;
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;
    private Integer sex;
    private String avatar;
    @NotBlank(message = "密码不能为空")
    private String password;
    private Integer status;
}
