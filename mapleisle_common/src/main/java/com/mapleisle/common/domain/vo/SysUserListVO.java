package com.mapleisle.common.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @project mapleisle_fast_dev
 * @description
 * @date 2025/8/6 15::06:56
 */
@Data
public class SysUserListVO {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private Integer sex;

    /**
     * 性别文本（用于前端直接展示）
     */
    private String sexLabel;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;

    /**
     * 状态文本（用于前端直接展示）
     */
    private String statusLabel;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    // Getter和Setter可通过lombok的@Data注解自动生成

    // 可以在VO中添加转换方法，将性别和状态的数字转为文本
    public void setSex(Integer sex) {
        this.sex = sex;
        switch (sex) {
            case 0:
                this.sexLabel = "男";
                break;
            case 1:
                this.sexLabel = "女";
                break;
            default:
                this.sexLabel = "未知";
        }
    }

    public void setStatus(Integer status) {
        this.status = status;
        this.statusLabel = status == 0 ? "正常" : "停用";
    }
}