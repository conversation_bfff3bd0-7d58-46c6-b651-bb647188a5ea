package com.mapleisle.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mapleisle.common.domain.dto.AddSysUserDto;
import com.mapleisle.common.domain.entity.UmsSysUser;
import com.mapleisle.common.response.MapleisleResult;

/***
 *@title IUmsSysUserService
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/5 14:43
 **/
public interface IUmsSysUserService extends IService<UmsSysUser> {
    MapleisleResult addSysUser(AddSysUserDto sysUserDto);

    MapleisleResult searchSysUserList();
}
