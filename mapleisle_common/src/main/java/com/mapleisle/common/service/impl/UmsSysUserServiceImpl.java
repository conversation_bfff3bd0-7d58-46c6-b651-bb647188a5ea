package com.mapleisle.common.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mapleisle.common.domain.dto.AddSysUserDto;
import com.mapleisle.common.domain.entity.UmsSysUser;
import com.mapleisle.common.domain.vo.SysUserListVO;
import com.mapleisle.common.mapper.UmsSysUserMapper;
import com.mapleisle.common.response.MapleisleResult;
import com.mapleisle.common.service.IUmsSysUserService;
import org.springframework.stereotype.Service;

import java.util.List;

/***
 *@title UmsSysUserServiceImpl
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/5 14:45
 **/
@Service
public class UmsSysUserServiceImpl extends ServiceImpl<UmsSysUserMapper, UmsSysUser> implements IUmsSysUserService {
    @Override
    public MapleisleResult addSysUser(AddSysUserDto sysUserDto) {
        // 1. 转换为 entity 类
        UmsSysUser umsSysUser = BeanUtil.copyProperties(sysUserDto, UmsSysUser.class);
        // 2. 新增
        int count = baseMapper.insert(umsSysUser);
        return count == 1 ? MapleisleResult.success("新增用户成功") : MapleisleResult.error("新增用户失败");
    }

    @Override
    public MapleisleResult searchSysUserList() {
        // 1. 查询所有用户
        List<UmsSysUser> umsSysUsers = baseMapper.selectList(null);
        // 2. 转换为对应 entity 集合类
        List<SysUserListVO> sysUserListVOS = BeanUtil.copyToList(umsSysUsers, SysUserListVO.class);
        return MapleisleResult.success(sysUserListVOS);
    }
}
