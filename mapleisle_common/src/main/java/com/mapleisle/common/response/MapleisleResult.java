package com.mapleisle.common.response;

import cn.hutool.core.util.ObjectUtil;
import com.mapleisle.common.constants.HttpStatus;

import java.io.Serial;
import java.util.HashMap;

/***
 *@title MapleisleResult
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/5 15:18
 **/
public class Maple<PERSON>leResult extends HashMap<String, Object> {
    // 状态码
    public static final String CODE_TAG = "code";
    // 返回内容
    public static final String MSG_TAG = "msg";
    // 数据对象
    public static final String DATA_TAG = "data";
    @Serial
    private static final long serialVersionUID = 3619127750409901909L;

    public MapleisleResult() {
    }

    public MapleisleResult(int code, String msg) {
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
    }

    public MapleisleResult(int code, String msg, Object data) {
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
        if (ObjectUtil.isNotEmpty(data)) super.put(DATA_TAG, data);
    }

    // 成功
    public static MapleisleResult success() {
        return MapleisleResult.success("操作成功");
    }

    public static MapleisleResult success(String msg) {
        return MapleisleResult.success(msg, null);
    }

    public static MapleisleResult success(Object data) {
        return MapleisleResult.success("操作成功", data);
    }

    private static MapleisleResult success(String msg, Object data) {
        return new MapleisleResult(HttpStatus.SUCCESS, msg, data);
    }

    // 失败
    public static MapleisleResult error() {
        return MapleisleResult.error("操作失败");
    }

    public static MapleisleResult error(String msg) {
        return MapleisleResult.error(msg, null);
    }

    public static MapleisleResult error(int code, String msg) {
        return new MapleisleResult(code, msg, null);
    }

    public static MapleisleResult error(String msg, Object data) {
        return new MapleisleResult(HttpStatus.ERROR, msg, data);
    }

    // 警告
    private static MapleisleResult warn(String msg) {
        return MapleisleResult.warn(msg, null);
    }

    public static MapleisleResult warn(String msg, Object data) {
        return new MapleisleResult(HttpStatus.WARN, msg, data);
    }

    // 判断
    public boolean isSuccess() {
        return ObjectUtil.equals(HttpStatus.SUCCESS, this.get(CODE_TAG));
    }

    public boolean isError() {
        return ObjectUtil.equals(HttpStatus.ERROR, this.get(CODE_TAG));
    }

    public boolean isWarn() {
        return ObjectUtil.equals(HttpStatus.WARN, this.get(CODE_TAG));
    }


    @Override
    public MapleisleResult put(String key, Object value) {
        super.put(key, value);
        return this;
    }
}
