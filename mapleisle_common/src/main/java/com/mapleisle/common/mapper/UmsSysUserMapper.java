package com.mapleisle.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mapleisle.common.domain.entity.UmsSysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/***
 *@title UmsSysUserMapper
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/5 14:35
 **/
@Mapper
public interface UmsSysUserMapper extends BaseMapper<UmsSysUser> {
    UmsSysUser selectUserByUserName(@Param("username") String username,
                                    @Param("accountType") int accountType);
}
