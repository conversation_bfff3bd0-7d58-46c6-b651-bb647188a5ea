package com.mapleisle.support.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/***
 *@title MyMetaObjectHandler
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/6 14:23
 **/
@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("开始插入填充...");
        this.strictInsertFill(metaObject, "createTime", LocalDateTime::now, LocalDateTime.class);
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime::now, LocalDateTime.class);
        // TODO admin 应该从登录用户获取
        this.strictInsertFill(metaObject, "creator", String.class, "admin");
        this.strictInsertFill(metaObject, "updater", String.class, "admin");
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("开始更新填充...");
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime::now, LocalDateTime.class);
        this.strictUpdateFill(metaObject, "updater", String.class, "admin");
    }
}
