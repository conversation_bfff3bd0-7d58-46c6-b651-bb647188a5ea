package com.mapleisle.support.config;

import com.mapleisle.common.domain.entity.UmsSysUser;
import com.mapleisle.common.mapper.UmsSysUserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/***
 *@title SysUserDetailService
 *@description <TODO description class purpose>
 *<AUTHOR>
 *@version 1.0.0
 *@create 2025/8/7 16:54
 **/
@Service
@RequiredArgsConstructor
public class SysUserDetailService implements UserDetailsService {
    private UmsSysUserMapper sysUserMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // TODO 验证登录类型
        int accountType = 0;
        // 根据账号查询用户，同时查询角色
        UmsSysUser sysUser = sysUserMapper.selectUserByUserName(username, accountType);
        // 根据角色查询权限
        return null;
    }
}
