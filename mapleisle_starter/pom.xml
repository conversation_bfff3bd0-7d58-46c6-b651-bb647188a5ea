<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mapleisle</groupId>
        <artifactId>mapleisle_fast_dev</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>mapleisle_starter</artifactId>
    <description>mapleisle-启动器模块</description>
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 其他业务模块 -->
        <dependency>
            <groupId>com.mapleisle</groupId>
            <artifactId>mapleisle_auth</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mapleisle</groupId>
            <artifactId>mapleisle_sys_user</artifactId>
        </dependency>
        <!-- web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>

</project>